import 'package:equatable/equatable.dart';

class FloorPlanModel extends Equatable {
  final String? nameAr;
  final String? nameEn;

  const FloorPlanModel({
    this.nameAr,
    this.nameEn,
  });

  factory FloorPlanModel.fromJson(Map<String, dynamic> json) {
    return FloorPlanModel(
      nameAr: json['name']?['ar'] as String?,
      nameEn: json['name']?['en'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = {
      'ar': nameAr,
      'en': nameEn,
    };
    return data;
  }

  FloorPlanModel copyWith({
    String? nameAr,
    String? nameEn,
  }) {
    return FloorPlanModel(
      nameAr: nameAr ?? this.nameAr,
      nameEn: nameEn ?? this.nameEn,
    );
  }

  @override
  List<Object?> get props => [nameAr, nameEn];
}
