import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../../../../../generated/l10n.dart';
import '../../../../core/shared_widgets/ad_linear_progress_indicator.dart';
import '../../../../core/shared_widgets/snack_bar.dart';
import '../../../../core/utils/resources.dart';
import '../../../models/floor_plan_model.dart';
import '../../../models/other_settings.dart';
import '../../../models/price_plan_model.dart';
import '../../../models/project_plan_model.dart';
import '../../../models/property_status_model.dart';
import '../../../repository/project_repository.dart';
import 'widgets/basic_information_section.dart';
import 'widgets/floor_plans_section.dart';
import 'widgets/gallery_images_section.dart';
import 'widgets/location_section.dart';
import 'widgets/project_plans_section.dart';

class AddProjectPage extends HookWidget {
  const AddProjectPage({super.key});

  @override
  Widget build(BuildContext context) {
    // Controllers for basic information
    final nameArController = useTextEditingController();
    final nameEnController = useTextEditingController();
    final descriptionArController = useTextEditingController();
    final descriptionEnController = useTextEditingController();

    // Selection states
    final selectedType = useState<OtherSettingsModel?>(null);
    final selectedPropertyStatus = useState<PropertyStatusModel?>(null);
    final selectedPricePlan = useState<PricePlanModel?>(null);
    final selectedPaymentMethod = useState<String?>(null);
    final selectedLocation = useState<OtherSettingsModel?>(null);

    // Location states
    final markers = useState<Set<Marker>?>(null);

    // Project plans and floor plans
    final projectPlans =
        useState<List<ProjectPlanModel>>([const ProjectPlanModel()]);
    final floorPlans = useState<List<FloorPlanModel>>([const FloorPlanModel()]);

    // Featured settings
    final featuredHome = useState<bool>(false);
    final featuredCategory = useState<bool>(false);

    // Gallery images
    final images = useState<List<File>?>(null);

    // Loading state
    final isLoading = useState<bool>(false);

    // Repository
    final projectRepository = ProjectRepository();

    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: GlobalColors.primaryColor,
          centerTitle: true,
          title: Text(
            S.of(context).addNewProject,
            style: const TextStyle(color: Colors.white),
          ),
          iconTheme: const IconThemeData(color: Colors.white),
        ),
        body: SingleChildScrollView(
          child: Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Basic Information Section
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(width: 0.5, color: Colors.grey),
                    color: Colors.white,
                  ),
                  child: ExpansionTile(
                    shape: const RoundedRectangleBorder(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(10),
                        topRight: Radius.circular(10),
                      ),
                    ),
                    title: Text(
                      S.of(context).Basicinformation,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    initiallyExpanded: true,
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: BasicInformationSection(
                          nameArController: nameArController,
                          nameEnController: nameEnController,
                          descriptionArController: descriptionArController,
                          descriptionEnController: descriptionEnController,
                          selectedType: selectedType,
                          selectedPropertyStatus: selectedPropertyStatus,
                          selectedPricePlan: selectedPricePlan,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 10),

                // Location Section
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(width: 0.5, color: Colors.grey),
                    color: Colors.white,
                  ),
                  child: ExpansionTile(
                    shape: const RoundedRectangleBorder(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(10),
                        topRight: Radius.circular(10),
                      ),
                    ),
                    title: Text(
                      S.of(context).Locationation,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    initiallyExpanded: false,
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: LocationSection(
                          markers: markers,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 10),

                // Project Plans Section
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(width: 0.5, color: Colors.grey),
                    color: Colors.white,
                  ),
                  child: ExpansionTile(
                    shape: const RoundedRectangleBorder(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(10),
                        topRight: Radius.circular(10),
                      ),
                    ),
                    title: Text(
                      S.of(context).projectPlans,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    initiallyExpanded: false,
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: ProjectPlansSection(
                          projectPlans: projectPlans,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 10),

                // Floor Plans Section
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(width: 0.5, color: Colors.grey),
                    color: Colors.white,
                  ),
                  child: ExpansionTile(
                    shape: const RoundedRectangleBorder(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(10),
                        topRight: Radius.circular(10),
                      ),
                    ),
                    title: Text(
                      S.of(context).floorPlans,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    initiallyExpanded: false,
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: FloorPlansSection(
                          floorPlans: floorPlans,
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 10),

                // Gallery Images Section
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(width: 0.5, color: Colors.grey),
                    color: Colors.white,
                  ),
                  child: ExpansionTile(
                    shape: const RoundedRectangleBorder(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(10),
                        topRight: Radius.circular(10),
                      ),
                    ),
                    title: Text(
                      S.of(context).galleryImages,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    initiallyExpanded: false,
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: GalleryImagesSection(
                          images: images,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 40),

                // Submit Button
                !isLoading.value
                    ? SizedBox(
                        height: 60,
                        width: MediaQuery.of(context).size.width,
                        child: ElevatedButton(
                          onPressed: () => _submitProject(
                            context,
                            nameArController,
                            nameEnController,
                            descriptionArController,
                            descriptionEnController,
                            selectedType,
                            selectedPropertyStatus,
                            selectedPricePlan,
                            markers,
                            projectPlans,
                            floorPlans,
                            featuredHome,
                            featuredCategory,
                            images,
                            isLoading,
                            projectRepository,
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: GlobalColors.primaryColor,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                          child: Text(
                            S.of(context).addProject,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      )
                    : const ADLinearProgressIndicator(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _submitProject(
    BuildContext context,
    TextEditingController nameArController,
    TextEditingController nameEnController,
    TextEditingController descriptionArController,
    TextEditingController descriptionEnController,
    ValueNotifier<OtherSettingsModel?> selectedType,
    ValueNotifier<PropertyStatusModel?> selectedPropertyStatus,
    ValueNotifier<PricePlanModel?> selectedPricePlan,
    ValueNotifier<Set<Marker>?> markers,
    ValueNotifier<List<ProjectPlanModel>> projectPlans,
    ValueNotifier<List<FloorPlanModel>> floorPlans,
    ValueNotifier<bool> featuredHome,
    ValueNotifier<bool> featuredCategory,
    ValueNotifier<List<File>?> images,
    ValueNotifier<bool> isLoading,
    ProjectRepository projectRepository,
  ) async {
    // Validation
    if (nameArController.text.isEmpty) {
      snackbar(S.of(context).pleaseEnterArabicName);
      return;
    }
    if (nameEnController.text.isEmpty) {
      snackbar(S.of(context).pleaseEnterEnglishName);
      return;
    }
    if (descriptionArController.text.isEmpty) {
      snackbar(S.of(context).pleaseEnterArabicDescription);
      return;
    }
    if (descriptionEnController.text.isEmpty) {
      snackbar(S.of(context).pleaseEnterEnglishDescription);
      return;
    }
    if (selectedType.value == null) {
      snackbar(S.of(context).pleaseSelectType);
      return;
    }
    if (selectedPropertyStatus.value == null) {
      snackbar(S.of(context).pleaseSelectPropertyStatus);
      return;
    }
    if (selectedPricePlan.value == null) {
      snackbar(S.of(context).pleaseSelectPricePlan);
      return;
    }
    if (markers.value == null || markers.value!.isEmpty) {
      snackbar(S.of(context).pleaseSelectLocation);
      return;
    }

    isLoading.value = true;

    try {
      // Prepare form data
      final formData = FormData.fromMap({
        'name[ar]': nameArController.text,
        'name[en]': nameEnController.text,
        'description[ar]': descriptionArController.text,
        'description[en]': descriptionEnController.text,
        'category_id': 8, // Properties category
        'price_plan_id': selectedPricePlan.value!.id,
        'latitude': markers.value!.first.position.latitude,
        'longitude': markers.value!.first.position.longitude,
        'featuredHome': featuredHome.value ? 1 : 0,
        'featuredCategory': featuredCategory.value ? 1 : 0,
        'type_id': selectedType.value!.id,
        'property_status': selectedPropertyStatus.value!.id,
      });

      // Add project plans
      for (int i = 0; i < projectPlans.value.length; i++) {
        final plan = projectPlans.value[i];
        if (plan.bedroomsAr?.isNotEmpty == true) {
          formData.fields.add(
              MapEntry('project_plans[$i][bedrooms][ar]', plan.bedroomsAr!));
        }
        if (plan.bedroomsEn?.isNotEmpty == true) {
          formData.fields.add(
              MapEntry('project_plans[$i][bedrooms][en]', plan.bedroomsEn!));
        }
        if (plan.priceFrom?.isNotEmpty == true) {
          formData.fields
              .add(MapEntry('project_plans[$i][price_from]', plan.priceFrom!));
        }
        if (plan.priceTo?.isNotEmpty == true) {
          formData.fields
              .add(MapEntry('project_plans[$i][price_to]', plan.priceTo!));
        }
        if (plan.spaceSizeAr?.isNotEmpty == true) {
          formData.fields.add(
              MapEntry('project_plans[$i][space_size][ar]', plan.spaceSizeAr!));
        }
        if (plan.spaceSizeEn?.isNotEmpty == true) {
          formData.fields.add(
              MapEntry('project_plans[$i][space_size][en]', plan.spaceSizeEn!));
        }
      }

      // Add floor plans
      for (int i = 0; i < floorPlans.value.length; i++) {
        final plan = floorPlans.value[i];
        if (plan.nameAr?.isNotEmpty == true) {
          formData.fields
              .add(MapEntry('floor_plans[$i][name][ar]', plan.nameAr!));
        }
        if (plan.nameEn?.isNotEmpty == true) {
          formData.fields
              .add(MapEntry('floor_plans[$i][name][en]', plan.nameEn!));
        }
      }

      // Add gallery images
      if (images.value != null) {
        for (int i = 0; i < images.value!.length; i++) {
          formData.files.add(MapEntry(
            "image[]",
            await MultipartFile.fromFile(images.value![i].path),
          ));
        }
      }

      // Submit to API
      final response = await projectRepository.addProject(formData);

      if (response.code == 1) {
        snackbar(S.of(context).projectAddedSuccessfully);
        Navigator.pop(context);
      } else {
        snackbar(response.msg ?? S.of(context).failedToAddProject);
      }
    } catch (e) {
      snackbar('Error: $e');
    } finally {
      isLoading.value = false;
    }
  }
}
