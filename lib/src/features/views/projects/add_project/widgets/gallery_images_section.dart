import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import '../../../../../generated/l10n.dart';
import '../../../../../core/shared_widgets/ad_file_picker.dart';

class GalleryImagesSection extends HookWidget {
  final ValueNotifier<List<File>?> images;

  const GalleryImagesSection({
    super.key,
    required this.images,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Gallery Images Label
        Text(
          S.of(context).UploadImage,
          style: const TextStyle(fontSize: 13),
        ),
        const SizedBox(height: 10),

        // File Picker for Multiple Images
        ADFilePicker(
          onFilesSelected: (selectedImages) {
            images.value = selectedImages;
          },
          title: S.of(context).Tabheretouploadimage,
          type: FileType.media,
          isMultiple: true,
        ),
        const SizedBox(height: 10),

        // Display selected images count
        if (images.value != null && images.value!.isNotEmpty)
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: Colors.blue[50],
              border: Border.all(color: Colors.blue[200]!),
            ),
            child: Row(
              children: [
                const Icon(Icons.image, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  '${images.value!.length} ${images.value!.length == 1 ? S.of(context).imageSelected : S.of(context).imagesSelected}',
                  style: const TextStyle(
                    color: Colors.blue,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }
}
