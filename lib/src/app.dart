import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

import '../generated/l10n.dart';
import '../main.dart';
import 'core/theme/ad_theme.dart';
import 'features/bloc/auth_blok.dart';
import 'features/views/splash/first_splash.dart';

class MyApp extends StatelessWidget {
  const MyApp({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Builder(builder: (context) {
      return MaterialApp(
        title: 'Dubai Page Admin',
        theme: ADTheme.lightTheme,
        debugShowCheckedModeBanner: false,
        localizationsDelegates: const [
          S.delegate,
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [
          Locale('en', ''),
          Locale('ar', ''),
        ],
        localeResolutionCallback: (currentLang, supportedLang) {
          String? lang = sharedPreferences!.getString("lang");
          AuthBloc.isEnglish = true;
          if (lang != null) {
            AuthBloc.isEnglish = lang == "en";
            return Locale(lang);
          }
          return supportedLang.first;
        },
        builder: (context, child) {
          return Directionality(
            // textDirection:
            // AuthBloc.isEnglish ? TextDirection.ltr : TextDirection.rtl,
            textDirection: TextDirection.ltr,
            child: Builder(
              builder: (BuildContext context) {
                return MediaQuery(
                  data: MediaQuery.of(context).copyWith(
                    textScaleFactor: 1.0,
                  ),
                  child: child!,
                );
              },
            ),
          );
        },
        home: const SplashPage(),
      );
    });
  }
}
